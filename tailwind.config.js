/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
      },
      colors: {
        blue: {
          800: '#0000ff',
          700: '#0033ff',
          600: '#2563EB',
          500: '#0044ff',
          300: '#60A5FA',
          400: '#90CDF4',
        },
        green: {
          500: '#25D366',
          600: '#128C7E',
        }
      },
      animation: {
        'fade-in': 'fadeIn 0.8s ease-out',
        'fade-in-up': 'fadeInUp 0.8s ease-out',
        'bubble-rise': 'bubbleRise 10s linear infinite',
        'bubble-rise-slow': 'bubbleRise 15s linear infinite',
        'bubble-rise-fast': 'bubbleRise 8s linear infinite',
        'bubble-rise-delayed': 'bubbleRise 12s linear infinite',
        'bubble-sway': 'bubbleSway 8s ease-in-out infinite',
        'bubble-sway-reverse': 'bubbleSwayReverse 10s ease-in-out infinite',
        'spin-glow': 'spin-glow 1s radial-gradient infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        bubbleRise: {
          '0%': {
            transform: 'translateY(20vh) scale(0.8)',
            opacity: '0'
          },
          '5%': {
            opacity: '0.6'
          },
          '95%': {
            opacity: '0.6'
          },
          '100%': {
            transform: 'translateY(-120vh) scale(1.2)',
            opacity: '0'
          },
        },
        bubbleSway: {
          '0%, 100%': {
            transform: 'translateX(0px)'
          },
          '25%': {
            transform: 'translateX(15px)'
          },
          '50%': {
            transform: 'translateX(-10px)'
          },
          '75%': {
            transform: 'translateX(20px)'
          },
        },
        bubbleSwayReverse: {
          '0%, 100%': {
            transform: 'translateX(0px)'
          },
          '25%': {
            transform: 'translateX(-20px)'
          },
          '50%': {
            transform: 'translateX(15px)'
          },
          '75%': {
            transform: 'translateX(-10px)'
          },
        },
        'spin-glow': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
      },
    },
  },
  plugins: [],
};